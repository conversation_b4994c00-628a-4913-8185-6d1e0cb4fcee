<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Unit\FieldAvailabilityConflictTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" tests="14" assertions="25" errors="1" failures="4" skipped="0" time="0.162856">
    <testcase name="it_detects_no_conflict_for_adjacent_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="33" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.095997"/>
    <testcase name="it_detects_overlapping_conflicts_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="52" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.009755">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityConflictTest::it_detects_overlapping_conflicts_correctly
Failed asserting that false is true.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php:66</failure>
    </testcase>
    <testcase name="it_detects_no_conflict_for_non_overlapping_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="76" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="4" time="0.003693"/>
    <testcase name="it_excludes_specified_reservation_from_conflict_check" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="97" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003789">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityConflictTest::it_excludes_specified_reservation_from_conflict_check
Failed asserting that false is true.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php:128</failure>
    </testcase>
    <testcase name="it_ignores_cancelled_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="138" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003315"/>
    <testcase name="it_validates_time_format_inputs" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="157" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003208"/>
    <testcase name="it_validates_end_time_format" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="168" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003008"/>
    <testcase name="it_validates_time_order" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="178" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003291"/>
    <testcase name="it_validates_same_start_and_end_time" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="188" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003174"/>
    <testcase name="it_handles_multiple_existing_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="198" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.004218">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityConflictTest::it_handles_multiple_existing_reservations
Failed asserting that false is true.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php:220</failure>
    </testcase>
    <testcase name="it_only_checks_conflicts_for_same_field" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="233" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.018425"/>
    <testcase name="it_only_checks_conflicts_for_same_date" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="252" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.004007"/>
    <testcase name="it_accepts_various_valid_time_formats" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="271" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="0" time="0.003242">
      <error type="InvalidArgumentException">Tests\Unit\FieldAvailabilityConflictTest::it_accepts_various_valid_time_formats
InvalidArgumentException: End time (11:00) must be after start time (9:00)

/Users/<USER>/Projects/Clients/FPMP/smp_online/app/Services/FieldAvailabilityService.php:79
/Users/<USER>/Projects/Clients/FPMP/smp_online/app/Services/FieldAvailabilityService.php:45
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php:276</error>
    </testcase>
    <testcase name="it_handles_edge_case_boundary_times" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="283" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="3" time="0.003733">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityConflictTest::it_handles_edge_case_boundary_times
Failed asserting that false is true.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php:299</failure>
    </testcase>
  </testsuite>
</testsuites>
